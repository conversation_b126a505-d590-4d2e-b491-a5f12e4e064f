import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:imtrans/services/local_ocr_service.dart';

/// Service for managing JavaScript-based text overlays in WebView
/// Handles injection, positioning, and cleanup of translated text overlays
class WebViewOverlayService {
  static final WebViewOverlayService _instance = WebViewOverlayService._internal();
  factory WebViewOverlayService() => _instance;
  WebViewOverlayService._internal();

  bool _isInitialized = false;
  bool _overlaysActive = false;
  InAppWebViewController? _currentController;
  final List<String> _activeOverlayIds = [];

  /// Initialize the overlay service
  Future<void> initialize() async {
    try {
      _isInitialized = true;
      debugPrint('WebViewOverlayService: Initialized successfully');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize - $e');
      throw OverlayException('Failed to initialize overlay service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if overlays are currently active
  bool get overlaysActive => _overlaysActive;

  /// Set the current WebView controller
  void setController(InAppWebViewController? controller) {
    _currentController = controller;
  }

  /// Inject CSS styles for overlays
  Future<void> _injectOverlayStyles() async {
    if (_currentController == null) return;

    const cssStyles = '''
      <style id="translation-overlay-styles">
        .translation-overlay {
          position: absolute;
          background-color: rgba(255, 255, 255, 0.9);
          border: 1px solid #CDEE2D;
          border-radius: 4px;
          padding: 2px 4px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 500;
          color: #333;
          z-index: 10000;
          pointer-events: none;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          transition: opacity 0.2s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 300px;
        }
        
        .translation-overlay.fade-in {
          opacity: 1;
        }
        
        .translation-overlay.fade-out {
          opacity: 0;
        }
        
        .translation-overlay-container {
          position: relative;
          z-index: 9999;
        }

        .translation-loading-overlay {
          position: absolute;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 8px;
          border-radius: 50%;
          font-size: 12px;
          z-index: 9999;
          pointer-events: none;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .image-overlay-container {
          position: relative;
          display: inline-block;
        }
      </style>
    ''';

    try {
      await _currentController!.evaluateJavascript(source: '''
        if (!document.getElementById('translation-overlay-styles')) {
          document.head.insertAdjacentHTML('beforeend', `$cssStyles`);
        }
      ''');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject styles - $e');
    }
  }

  /// Inject JavaScript functions for overlay management
  Future<void> _injectOverlayScripts() async {
    if (_currentController == null) return;

    const jsScript = '''
      window.translationOverlays = window.translationOverlays || {
        overlays: new Map(),
        loadingOverlays: new Map(),
        scrollHandler: null,
        intersectionObserver: null,
        imageCache: new Map(),
        processingImages: new Set(),

        // Initialize intersection observer for lazy processing
        initIntersectionObserver: function() {
          if (this.intersectionObserver) return;

          this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;
                const imageUrl = img.src;

                // Check if image is already processed or being processed
                if (!this.imageCache.has(imageUrl) && !this.processingImages.has(imageUrl)) {
                  console.log('Image entered viewport, ready for processing:', imageUrl);
                  // Trigger processing event that Flutter can listen to
                  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                    window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
                  }
                }
              }
            });
          }, {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
          });

          // Observe all images
          document.querySelectorAll('img').forEach(img => {
            this.intersectionObserver.observe(img);
          });
        },

        // Show loading indicator on image
        showLoadingIndicator: function(imageUrl) {
          const images = document.querySelectorAll('img');
          let targetImage = null;

          for (let img of images) {
            if (img.src === imageUrl || img.src.includes(imageUrl.split('/').pop())) {
              targetImage = img;
              break;
            }
          }

          if (!targetImage) return;

          this.processingImages.add(imageUrl);

          const imageRect = targetImage.getBoundingClientRect();
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          const loadingOverlay = document.createElement('div');
          loadingOverlay.id = 'loading-overlay-' + Date.now();
          loadingOverlay.className = 'translation-loading-overlay';
          loadingOverlay.innerHTML = '⟳';

          // Position at center of image
          loadingOverlay.style.left = (imageRect.left + scrollX + imageRect.width / 2 - 20) + 'px';
          loadingOverlay.style.top = (imageRect.top + scrollY + imageRect.height / 2 - 20) + 'px';

          document.body.appendChild(loadingOverlay);
          this.loadingOverlays.set(imageUrl, loadingOverlay);

          console.log('Loading indicator shown for:', imageUrl);
        },

        // Hide loading indicator
        hideLoadingIndicator: function(imageUrl) {
          const loadingOverlay = this.loadingOverlays.get(imageUrl);
          if (loadingOverlay) {
            loadingOverlay.remove();
            this.loadingOverlays.delete(imageUrl);
          }
          this.processingImages.delete(imageUrl);
          console.log('Loading indicator hidden for:', imageUrl);
        },

        createImageOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
          console.log('Creating overlay for:', id, text, imageUrl, x, y, width, height, fontSize);

          // Hide loading indicator if present
          this.hideLoadingIndicator(imageUrl);

          // Find the image element
          const images = document.querySelectorAll('img');
          let targetImage = null;

          for (let img of images) {
            console.log('Checking image:', img.src);
            if (img.src === imageUrl || img.src.includes(imageUrl.split('/').pop())) {
              targetImage = img;
              console.log('Found target image:', img.src);
              break;
            }
          }

          if (!targetImage) {
            console.warn('Target image not found for overlay:', imageUrl);
            console.log('Available images:', Array.from(images).map(img => img.src));
            // Fallback to absolute positioning
            return this.createOverlay(id, text, x, y, width, height, fontSize);
          }

          // Wait for image to load if necessary
          if (!targetImage.complete || targetImage.naturalWidth === 0) {
            console.log('Image not loaded yet, waiting...');
            targetImage.onload = () => {
              this.createImageOverlay(id, text, imageUrl, x, y, width, height, fontSize);
            };
            return null;
          }

          // Get image position and size
          const imageRect = targetImage.getBoundingClientRect();
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          console.log('Image rect:', imageRect);
          console.log('Natural size:', targetImage.naturalWidth, targetImage.naturalHeight);

          // Calculate scale factors - ensure image coordinates are relative to image bounds
          const scaleX = imageRect.width / targetImage.naturalWidth;
          const scaleY = imageRect.height / targetImage.naturalHeight;

          // Calculate absolute position - constrain within image boundaries
          const relativeX = Math.max(0, Math.min(x, targetImage.naturalWidth));
          const relativeY = Math.max(0, Math.min(y, targetImage.naturalHeight));

          const absoluteX = imageRect.left + scrollX + (relativeX * scaleX);
          const absoluteY = imageRect.top + scrollY + (relativeY * scaleY);

          // Ensure overlay stays within image bounds
          const maxX = imageRect.left + scrollX + imageRect.width - 10;
          const maxY = imageRect.top + scrollY + imageRect.height - 10;

          const constrainedX = Math.min(absoluteX, maxX);
          const constrainedY = Math.min(absoluteY, maxY);

          console.log('Constrained position:', constrainedX, constrainedY);

          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-' + id;
          overlay.className = 'translation-overlay';
          overlay.textContent = text;
          overlay.style.left = constrainedX + 'px';
          overlay.style.top = constrainedY + 'px';
          overlay.style.fontSize = fontSize + 'px';

          // Constrain overlay width to image bounds
          const maxOverlayWidth = Math.min(width * scaleX, imageRect.width - (constrainedX - imageRect.left - scrollX), 300);
          overlay.style.maxWidth = maxOverlayWidth + 'px';
          overlay.style.zIndex = '10000';

          document.body.appendChild(overlay);
          this.overlays.set(id, {
            element: overlay,
            targetImage: targetImage,
            originalX: relativeX,
            originalY: relativeY,
            scrollX: scrollX,
            scrollY: scrollY,
            imageUrl: imageUrl
          });

          // Mark image as processed in cache
          this.imageCache.set(imageUrl, true);

          console.log('Overlay created and added to DOM:', overlay);

          // Fade in animation
          setTimeout(() => {
            overlay.classList.add('fade-in');
            console.log('Fade in animation applied');
          }, 10);

          return overlay;
        },

        createOverlay: function(id, text, x, y, width, height, fontSize) {
          // Fallback to absolute positioning
          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-' + id;
          overlay.className = 'translation-overlay';
          overlay.textContent = text;
          overlay.style.left = x + 'px';
          overlay.style.top = y + 'px';
          overlay.style.fontSize = fontSize + 'px';
          overlay.style.minWidth = Math.min(width, 300) + 'px';
          overlay.style.maxWidth = '300px';

          document.body.appendChild(overlay);
          this.overlays.set(id, {
            element: overlay,
            originalX: x,
            originalY: y,
            scrollX: window.scrollX,
            scrollY: window.scrollY
          });

          // Fade in animation
          setTimeout(() => overlay.classList.add('fade-in'), 10);

          return overlay;
        },
        
        removeOverlay: function(id) {
          const overlayData = this.overlays.get(id);
          if (overlayData) {
            overlayData.element.classList.add('fade-out');
            setTimeout(() => {
              if (overlayData.element.parentNode) {
                overlayData.element.parentNode.removeChild(overlayData.element);
              }
              this.overlays.delete(id);
            }, 200);
          }
        },
        
        removeAllOverlays: function() {
          this.overlays.forEach((overlayData, id) => {
            this.removeOverlay(id);
          });
          this.overlays.clear();
        },
        
        updateOverlayPositions: function() {
          const currentScrollX = window.scrollX;
          const currentScrollY = window.scrollY;

          this.overlays.forEach((overlayData, id) => {
            if (overlayData.targetImage) {
              // Image-relative positioning
              const imageRect = overlayData.targetImage.getBoundingClientRect();
              const absoluteX = imageRect.left + currentScrollX + (overlayData.originalX * imageRect.width / overlayData.targetImage.naturalWidth);
              const absoluteY = imageRect.top + currentScrollY + (overlayData.originalY * imageRect.height / overlayData.targetImage.naturalHeight);

              overlayData.element.style.left = absoluteX + 'px';
              overlayData.element.style.top = absoluteY + 'px';
            } else {
              // Fallback to absolute positioning
              const deltaX = currentScrollX - overlayData.scrollX;
              const deltaY = currentScrollY - overlayData.scrollY;

              overlayData.element.style.left = (overlayData.originalX - deltaX) + 'px';
              overlayData.element.style.top = (overlayData.originalY - deltaY) + 'px';
            }
          });
        },
        
        setupScrollHandler: function() {
          if (this.scrollHandler) return;
          
          this.scrollHandler = () => {
            this.updateOverlayPositions();
          };
          
          window.addEventListener('scroll', this.scrollHandler, { passive: true });
          window.addEventListener('resize', this.scrollHandler, { passive: true });
        },
        
        removeScrollHandler: function() {
          if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
            window.removeEventListener('resize', this.scrollHandler);
            this.scrollHandler = null;
          }
        },

        // Initialize the system
        initialize: function() {
          this.initIntersectionObserver();
          this.setupScrollHandler();
          console.log('Translation overlay system initialized');
        },

        // Clear cache for specific image
        clearImageCache: function(imageUrl) {
          this.imageCache.delete(imageUrl);
          console.log('Cache cleared for:', imageUrl);
        },

        // Clear all cache
        clearAllCache: function() {
          this.imageCache.clear();
          console.log('All cache cleared');
        },

        // Check if image is cached
        isImageCached: function(imageUrl) {
          return this.imageCache.has(imageUrl);
        },

        // Get processing status
        isImageProcessing: function(imageUrl) {
          return this.processingImages.has(imageUrl);
        }
      };
    ''';

    try {
      await _currentController!.evaluateJavascript(source: jsScript);
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject scripts - $e');
    }
  }

  /// High-level method to show translation overlays
  Future<void> showTranslationOverlays({
    required List<OcrTextElement> translatedElements,
    required List<String> imageUrls,
  }) async {
    return showOverlays(translatedElements, imageUrls);
  }

  /// Initialize the overlay system with intersection observer
  Future<void> initializeOverlaySystem() async {
    if (_currentController == null) return;

    try {
      await _injectOverlayStyles();
      await _injectOverlayScripts();

      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.initialize();'
      );

      debugPrint('WebViewOverlayService: Overlay system initialized');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize overlay system - $e');
    }
  }

  /// Show loading indicator for a specific image
  Future<void> showLoadingIndicator(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.showLoadingIndicator('$imageUrl');
      ''');
      debugPrint('WebViewOverlayService: Loading indicator shown for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show loading indicator - $e');
    }
  }

  /// Hide loading indicator for a specific image
  Future<void> hideLoadingIndicator(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideLoadingIndicator('$imageUrl');
      ''');
      debugPrint('WebViewOverlayService: Loading indicator hidden for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide loading indicator - $e');
    }
  }

  /// Check if image is already cached/processed
  Future<bool> isImageCached(String imageUrl) async {
    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.isImageCached('$imageUrl');
      ''');
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to check cache status - $e');
      return false;
    }
  }

  /// Check if image is currently being processed
  Future<bool> isImageProcessing(String imageUrl) async {
    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.isImageProcessing('$imageUrl');
      ''');
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to check processing status - $e');
      return false;
    }
  }

  /// Clear cache for specific image
  Future<void> clearImageCache(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.clearImageCache('$imageUrl');
      ''');
      debugPrint('WebViewOverlayService: Cache cleared for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to clear image cache - $e');
    }
  }

  /// Clear all translation cache
  Future<void> clearAllCache() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.clearAllCache();
      ''');
      debugPrint('WebViewOverlayService: All cache cleared');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to clear all cache - $e');
    }
  }

  /// Create and display overlays for translated text elements
  Future<void> showOverlays(List<OcrTextElement> translatedElements, List<String> imageUrls) async {
    if (!_isInitialized || _currentController == null) {
      throw OverlayException('Overlay service not initialized or controller not set');
    }

    try {
      // Initialize the overlay system
      await initializeOverlaySystem();

      debugPrint('WebViewOverlayService: Creating overlays for ${translatedElements.length} elements and ${imageUrls.length} images');

      // Group elements by image (assuming elements are ordered by image)
      int elementsPerImage = translatedElements.length ~/ imageUrls.length;
      if (elementsPerImage == 0) elementsPerImage = 1;

      debugPrint('WebViewOverlayService: Elements per image: $elementsPerImage');

      // Create overlays for each translated element
      for (int i = 0; i < translatedElements.length; i++) {
        final element = translatedElements[i];
        final overlayId = 'overlay_$i';
        final imageIndex = i ~/ elementsPerImage;
        final imageUrl = imageIndex < imageUrls.length ? imageUrls[imageIndex] : imageUrls.last;

        final boundingBox = element.boundingBox;
        final fontSize = _calculateFontSize(boundingBox.width, boundingBox.height);

        debugPrint('WebViewOverlayService: Creating overlay $overlayId for text "${element.text}" at (${boundingBox.left}, ${boundingBox.top}) on image $imageUrl');

        // Create overlay positioned relative to the specific image
        try {
          await _currentController!.evaluateJavascript(source: '''
            window.translationOverlays.createImageOverlay(
              '$overlayId',
              ${_escapeJavaScriptString(element.text)},
              '$imageUrl',
              ${boundingBox.left},
              ${boundingBox.top},
              ${boundingBox.width},
              ${boundingBox.height},
              $fontSize
            );
          ''');

          _activeOverlayIds.add(overlayId);
          debugPrint('WebViewOverlayService: Successfully created overlay $overlayId');
        } catch (e) {
          debugPrint('WebViewOverlayService: Failed to create overlay $overlayId: $e');
        }
      }

      _overlaysActive = true;
      debugPrint('WebViewOverlayService: Created ${translatedElements.length} overlays for ${imageUrls.length} images');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show overlays - $e');
      throw OverlayException('Failed to show overlays: $e');
    }
  }

  /// Hide all overlays
  Future<void> hideOverlays() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeAllOverlays();'
      );
      
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeScrollHandler();'
      );

      _activeOverlayIds.clear();
      _overlaysActive = false;
      debugPrint('WebViewOverlayService: Hidden all overlays');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide overlays - $e');
    }
  }

  /// Clean up all overlays and handlers
  Future<void> cleanup() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await hideOverlays();
      
      // Remove injected styles
      await _currentController!.evaluateJavascript(source: '''
        const styleElement = document.getElementById('translation-overlay-styles');
        if (styleElement) {
          styleElement.parentNode.removeChild(styleElement);
        }
      ''');
      
      debugPrint('WebViewOverlayService: Cleanup completed');
    } catch (e) {
      debugPrint('WebViewOverlayService: Cleanup failed - $e');
    }
  }

  /// Calculate appropriate font size based on bounding box dimensions
  double _calculateFontSize(double width, double height) {
    // Base font size calculation based on bounding box height
    double fontSize = height * 0.7; // 70% of bounding box height
    
    // Clamp font size to reasonable bounds
    fontSize = fontSize.clamp(10.0, 18.0);
    
    return fontSize;
  }

  /// Escape JavaScript string to prevent injection attacks
  String _escapeJavaScriptString(String text) {
    return "'" + text
        .replaceAll('\\', '\\\\')
        .replaceAll("'", "\\'")
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t') + "'";
  }

  /// Dispose the service
  void dispose() {
    _currentController = null;
    _activeOverlayIds.clear();
    _overlaysActive = false;
    _isInitialized = false;
    debugPrint('WebViewOverlayService: Disposed');
  }
}

/// Custom exception for overlay operations
class OverlayException implements Exception {
  final String message;
  final dynamic originalError;

  const OverlayException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OverlayException: $message (Original error: $originalError)';
    }
    return 'OverlayException: $message';
  }
}
